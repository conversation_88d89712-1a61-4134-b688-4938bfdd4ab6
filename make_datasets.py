# 导入必要的库
import h5py
import numpy as np
import pandas as pd
import datetime
import logging
from pathlib import Path
import time as timer  # 避免与 hdf5 time 数据集名称冲突

# --- 配置参数 ---
# 输入文件路径
ORIGINAL_TEC_HDF5 = Path("TEC_Data.hdf5")  # 你的原始扁平化 HDF5 文件
OMNI_DIR = Path("./omni_data/")  # 包含 OMNI .lst 文件的目录
# OMNI 格式文件路径 (确保文件名正确)
OMNI_FMT_FILE = OMNI_DIR / "omni2_5lNdjako8e.fmt"  # <- 修正文件名
# 查找 OMNI 数据文件的模式
OMNI_FILE_PATTERN = "omni2_*.lst"

# 输出文件路径
OUTPUT_HDF5 = Path("CRIM_SW2hr_AI_v1.2_DataDrivenRange_CN.hdf5")  # 调整输出文件名

# 网格维度 (用于验证和重塑)
N_LAT = 41
N_LON = 71

# 目标时间分辨率 (0, 2, ..., 22 UTC)
TARGET_HOURS = list(range(0, 24, 2))

# 填充值 (重要提示: 请务必从 OMNI 官方文档核实这些填充值!)
TEC_FILL_VALUE = -9999.0
RMSE_FILL_VALUE = -9999.0  # 如果RMSE有不同的填充值，请修改
OMNI_FLOAT_FILL = 999.9  # OMNI 文档中用于 Scalar B, Proton Density, F107 等浮点数的填充值示例
OMNI_DST_AE_FILL = 99999  # OMNI 文档中用于 Dst, AE 等整数的填充值示例
OMNI_KP_AP_FILL = 999  # OMNI 文档中用于 Kp (*10), ap 等整数的填充值示例

# HDF5 压缩设置
COMPRESSION_TYPE = "gzip"  # 压缩算法
COMPRESSION_OPTS = 4  # 压缩级别 (0-9, 4 或 6 是常用平衡点)

# --- 日志记录设置 ---
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


# --- 辅助函数：解析 OMNI 格式文件 ---
def parse_omni_fmt(fmt_file):
    """
    解析 OMNI .fmt 文件以获取列名和列宽。
    这是一个简化的示例，可能需要根据 .fmt 文件的具体细微差别进行调整。
    """
    col_specs = []  # 存储列的起始和结束位置 [(start, end), ...]
    col_names = []  # 存储解析出的列名
    try:
        with open(fmt_file, "r") as f:
            lines = f.readlines()
            # 找到格式定义行的起始位置（可能需要根据文件微调）
            start_idx = 0
            for i, line in enumerate(lines):
                if line.strip().startswith("ITEMS"):  # 寻找 ITEMS 标题行
                    start_idx = i + 2  # 跳过标题行和空行
                    break

            # 针对特定的omni2_5lNdjako8e.fmt文件进行硬编码处理
            # 这是一个直接的解决方案，适用于已知格式的文件
            col_names = [
                "YEAR",
                "DOY",
                "Hour",
                "Scalar_B_nT",
                "SW_Proton_Density_N_cm_3",
                "Kp_index",
                "Dst_index_nT",
                "ap_index_nT",
                "f10_7_index",
                "AE_index_nT",
            ]

            # 根据.fmt文件内容手动设置列宽
            widths = [4, 4, 3, 6, 6, 3, 6, 4, 6, 5]

            # 计算列的起始和结束位置
            start_pos = 0
            for width in widths:
                end_pos = start_pos + width
                col_specs.append((start_pos, end_pos))
                start_pos = end_pos

    except FileNotFoundError:
        logging.error(f"OMNI 格式文件未找到: {fmt_file}")
        raise
    except Exception as e:
        logging.error(f"解析 OMNI 格式文件时出错: {e}")
        raise

    # 确保列名和列规格数量匹配
    if len(col_names) != len(col_specs):
        logging.warning(f"列名和列规格数量不匹配: 名称={len(col_names)}, 规格={len(col_specs)}")
        # 如果不匹配，可以采取一些策略，例如截断较长的列表
        min_len = min(len(col_names), len(col_specs))
        col_names = col_names[:min_len]
        col_specs = col_specs[:min_len]

    logging.info(f"从 OMNI 格式文件成功解析 {len(col_names)} 列。")
    return col_names, col_specs


# --- 主处理函数 ---
def main():
    start_time = timer.time()  # 记录开始时间
    logging.info("开始创建数据集...")

    # 1. 加载原始 TEC 数据
    logging.info(f"从 {ORIGINAL_TEC_HDF5} 加载原始 TEC 数据...")
    try:
        # 使用 h5py 打开原始 HDF5 文件（只读模式）
        with h5py.File(ORIGINAL_TEC_HDF5, "r") as src:
            # 读取 1D 数据集并用 flatten() 确保它们是一维的
            tec_1d = src["TEC"][:].flatten().astype(np.float32)
            rmse_1d = src["RMSE"][:].flatten().astype(np.float32)
            lat_1d = src["latitude"][:].flatten()
            lon_1d = src["longitude"][:].flatten()
            year_1d = src["year"][:].flatten()
            doy_1d = src["day_of_year"][:].flatten()
            hour_1d = src["hour"][:].flatten()
            # 如果需要，也可以加载原始文件的全局属性
            # original_tec_attrs = dict(src.attrs)
        logging.info(f"从原始文件成功加载 {len(tec_1d)} 个数据点。")
    except Exception as e:
        logging.error(f"加载原始 TEC 数据失败: {e}")
        return  # 加载失败则退出

    # 2. 确定唯一坐标 & 重构原始时间戳 & 确定日期范围
    logging.info("确定唯一坐标，重构时间戳，并查找日期范围...")
    # 找到唯一的纬度和经度值并排序
    unique_lats = np.sort(np.unique(lat_1d))
    unique_lons = np.sort(np.unique(lon_1d))
    # 验证维度是否匹配预期
    if len(unique_lats) != N_LAT or len(unique_lons) != N_LON:
        logging.warning(f"网格维度不匹配！找到 {len(unique_lats)} 个纬度, {len(unique_lons)} 个经度。")
        # 如果维度不匹配是预期之外的，可能需要在此处停止或调整 N_LAT/N_LON

    original_datetimes_utc = []  # 存储重构出的原始 datetime 对象
    min_datetime_orig = None  # 记录数据中的最早时间
    max_datetime_orig = None  # 记录数据中的最晚时间

    # 遍历原始数据，创建 datetime 对象
    for i in range(len(tec_1d)):
        try:
            year = int(year_1d[i])
            doy = int(doy_1d[i])
            hour = int(hour_1d[i])
            # 检查天数和小时是否在有效范围内
            if not (1 <= doy <= 366):  # 允许闰年
                raise ValueError(f"无效的年积日: {doy}")
            if not (0 <= hour <= 23):
                raise ValueError(f"无效的小时: {hour}")

            # 使用 datetime 从年、年积日、小时创建 UTC 时间对象
            # 注意：timedelta(days=doy-1) 会自动处理闰年
            dt_obj = datetime.datetime(year, 1, 1, hour, 0, 0, tzinfo=datetime.timezone.utc) + datetime.timedelta(days=doy - 1)
            original_datetimes_utc.append(dt_obj)

            # 更新数据覆盖的最早和最晚时间记录
            if min_datetime_orig is None or dt_obj < min_datetime_orig:
                min_datetime_orig = dt_obj
            if max_datetime_orig is None or dt_obj > max_datetime_orig:
                max_datetime_orig = dt_obj

        except Exception as e:
            # 如果时间转换失败，记录错误并添加缺失标记
            logging.error(f"为原始索引 {i} 创建 datetime 时出错: year={year}, doy={doy}, hour={hour}. 错误: {e}")
            original_datetimes_utc.append(pd.NaT)  # 使用 pandas NaT 表示无效或缺失时间

    # 将列表转换为 pandas Series，方便处理 NaT
    original_datetimes_utc = pd.Series(original_datetimes_utc)

    # 检查是否成功确定了日期范围
    if min_datetime_orig is None or max_datetime_orig is None:
        logging.error("无法从输入的 TEC 数据确定日期范围。退出。")
        return

    # 从实际数据范围确定起始和结束年份
    START_YEAR = min_datetime_orig.year
    END_YEAR = max_datetime_orig.year

    # 计算实际覆盖的开始和结束日期时间，确保对齐到 2 小时间隔
    # 从找到的第一天的 00:00 UTC 开始
    COVERAGE_START_DATE_DT = datetime.datetime(
        min_datetime_orig.year, min_datetime_orig.month, min_datetime_orig.day, 0, 0, 0, tzinfo=datetime.timezone.utc
    )
    # 到找到的最后一天的 22:00 UTC 结束
    COVERAGE_END_DATE_DT = datetime.datetime(
        max_datetime_orig.year, max_datetime_orig.month, max_datetime_orig.day, 22, 0, 0, tzinfo=datetime.timezone.utc
    )

    # 格式化为字符串
    COVERAGE_START_DATE = COVERAGE_START_DATE_DT.strftime("%Y-%m-%d %H:%M:%S")
    COVERAGE_END_DATE = COVERAGE_END_DATE_DT.strftime("%Y-%m-%d %H:%M:%S")

    logging.info(f"从数据确定年份范围: {START_YEAR}-{END_YEAR}")
    logging.info(f"有效覆盖开始时间: {COVERAGE_START_DATE}")
    logging.info(f"有效覆盖结束时间: {COVERAGE_END_DATE}")
    logging.info("原始时间戳重构完成。")

    # 3. 加载和处理 OMNI 数据 (根据确定的年份范围进行筛选)
    logging.info(f"加载并处理 {START_YEAR} 到 {END_YEAR} 年的 OMNI 数据...")
    try:
        # 解析 OMNI 格式文件获取列名和列宽
        omni_col_names, omni_col_specs = parse_omni_fmt(OMNI_FMT_FILE)
        col_widths = [spec[1] - spec[0] for spec in omni_col_specs]  # 计算每列宽度

        # 查找目录中所有匹配模式的 OMNI .lst 文件
        omni_files = sorted(OMNI_DIR.glob(OMNI_FILE_PATTERN))
        if not omni_files:  # 如果找不到文件，则报错退出
            raise FileNotFoundError(f"在 {OMNI_DIR} 中找不到匹配模式 {OMNI_FILE_PATTERN} 的 OMNI 文件")

        df_omni_list = []  # 用于存储每年读取的 DataFrame
        # 遍历找到的 OMNI 文件
        for file in omni_files:
            try:
                # 不再尝试从文件名提取年份，而是直接读取文件内容
                logging.info(f"正在读取 OMNI 文件: {file.name}")
                # 使用 pandas 读取固定宽度文件
                df_year = pd.read_fwf(file, widths=col_widths, names=omni_col_names, header=None)

                # 验证文件是否包含YEAR列，以及是否有数据
                if "YEAR" in df_year.columns and not df_year.empty:
                    # 检查文件中的年份是否在我们需要的范围内
                    file_years = df_year["YEAR"].unique()
                    valid_years = [year for year in file_years if START_YEAR <= year <= END_YEAR]

                    if valid_years:
                        # 文件中有我们需要的年份数据
                        logging.info(f"文件 {file.name} 包含年份 {valid_years}，属于目标范围 {START_YEAR}-{END_YEAR}")
                        # 只保留范围内的数据
                        df_year = df_year[df_year["YEAR"].isin(valid_years)]
                        df_omni_list.append(df_year)
                    else:
                        logging.warning(f"跳过 OMNI 文件 {file.name}，其年份 {file_years} 不在目标范围 {START_YEAR}-{END_YEAR} 内。")
                else:
                    logging.warning(f"跳过 OMNI 文件 {file.name}，未找到有效的 YEAR 列或文件为空。")
            except Exception as e:
                logging.error(f"处理 OMNI 文件 {file.name} 时出错: {e}")

        # 检查是否加载到了任何 OMNI 数据
        if not df_omni_list:
            logging.error(f"在所需年份范围 {START_YEAR}-{END_YEAR} 内找不到任何有效的 OMNI 数据。退出。")
            return

        # 将所有年份的 DataFrame 合并为一个
        df_omni = pd.concat(df_omni_list, ignore_index=True)
        logging.info(f"合并后的 OMNI 数据，初始行数: {len(df_omni)}")

        # --- 按 YEAR 列进行严格过滤 ---
        # 将 YEAR 列转换为数值型，无法转换的设为 NaN
        df_omni["YEAR"] = pd.to_numeric(df_omni["YEAR"], errors="coerce")
        df_omni = df_omni.dropna(subset=["YEAR"])  # 删除 YEAR 为 NaN 的行
        df_omni["YEAR"] = df_omni["YEAR"].astype(int)  # 转换为整数
        # 保留 YEAR 在指定范围内的行
        df_omni = df_omni[(df_omni["YEAR"] >= START_YEAR) & (df_omni["YEAR"] <= END_YEAR)]
        logging.info(f"按 YEAR 列过滤后的 OMNI 数据行数: {len(df_omni)}")

        # 将其他列（除时间相关列外）尝试转换为数值型，无法转换的值（如文本）变为 NaN
        for col in df_omni.columns:
            if col not in ["YEAR", "DOY", "Hour"]:
                df_omni[col] = pd.to_numeric(df_omni[col], errors="coerce")

        # --- 创建 datetime 索引 ---
        try:
            # 将 YEAR, DOY, Hour 合并为 datetime 对象 (设为 UTC)
            df_omni["datetime"] = pd.to_datetime(
                df_omni["YEAR"].astype(str)
                + "-"
                + df_omni["DOY"].astype(int).astype(str)
                + "-"  # 确保 DOY 是整数
                + df_omni["Hour"].astype(int).astype(str),  # 确保 Hour 是整数
                format="%Y-%j-%H",
                utc=True,
                errors="coerce",
            )  # 按年-年积日-小时格式解析
            df_omni = df_omni.dropna(subset=["datetime"])  # 删除无法转换 datetime 的行
            df_omni = df_omni.set_index("datetime")  # 将 datetime 列设为索引
            df_omni = df_omni.sort_index()  # 按时间排序
            # 仅保留在 TEC 数据精确覆盖范围内的数据 (加1小时缓冲确保包含结束点)
            df_omni = df_omni[COVERAGE_START_DATE_DT : COVERAGE_END_DATE_DT + datetime.timedelta(hours=1)]

        except Exception as e:
            logging.error(f"为 OMNI 数据创建 datetime 索引时出错: {e}")
            return

        logging.info(f"OMNI 数据已处理 datetime 索引。最终范围内的行数: {len(df_omni)}")

        # --- 处理 OMNI 填充值 (替换为 NaN 以便后续处理) ---
        # !!注意!!: 这里的列名 ('Scalar_B_nT'等) 取决于你的 parse_omni_fmt 函数的解析结果，必须匹配！
        fill_map = {
            "Scalar_B_nT": OMNI_FLOAT_FILL,
            "SW_Proton_Density_N_cm_3": OMNI_FLOAT_FILL,
            "Kp_index": OMNI_KP_AP_FILL,  # Kp 原始值
            "Dst_index_nT": OMNI_DST_AE_FILL,
            "ap_index_nT": OMNI_KP_AP_FILL,
            "f10_7_index": OMNI_FLOAT_FILL,
            "AE_index_nT": OMNI_DST_AE_FILL,
        }
        for col, fill_val in fill_map.items():
            if col in df_omni.columns:
                # 将 DataFrame 中等于 fill_val 的值替换为 NaN
                df_omni[col] = df_omni[col].replace(fill_val, np.nan)
            else:
                # 如果配置的列名在实际读取的 DataFrame 中不存在，发出警告
                logging.warning(f"OMNI 列 '{col}' 在 DataFrame 中未找到，无法处理填充值。")

    except Exception as e:
        logging.error(f"加载或处理 OMNI 数据过程中失败: {e}")
        return

    # 4. 准备目标时间网格 (2小时间隔, 基于确定的数据范围)
    logging.info("根据数据范围生成目标 2 小时时间网格...")
    try:
        # 使用 pandas 生成 2 小时频率的时间序列
        target_timestamps = pd.date_range(start=COVERAGE_START_DATE, end=COVERAGE_END_DATE, freq="2H", tz="UTC")
        N_times = len(target_timestamps)  # 获取总的时间点数量
        if N_times == 0:  # 如果生成的时间点为0，说明日期范围可能有问题
            logging.error("目标时间网格为空。请检查确定的日期范围。")
            return
        logging.info(f"目标时间网格已创建，包含 {N_times} 个时间戳。")
    except Exception as e:
        logging.error(f"创建目标时间网格失败: {e}")
        return

    # 5. 将 OMNI 数据对齐到目标时间网格
    logging.info("将 OMNI 数据对齐到 2 小时网格...")
    try:
        # 使用 reindex 将 OMNI DataFrame 的索引对齐到 target_timestamps
        # 缺失的时间点将用 NaN 填充 (method=None 是默认行为)
        df_omni_aligned = df_omni.reindex(target_timestamps, method=None)

        # 特殊处理 F10.7 (日值指数): 使用前向填充填充一天内的缺失值
        if "f10_7_index" in df_omni_aligned.columns:
            # 按日期分组，在每个日期组内进行前向填充
            df_omni_aligned["f10_7_index"] = df_omni_aligned.groupby(df_omni_aligned.index.date)["f10_7_index"].ffill()
            # 可选：再进行后向填充，以填补每天开始可能存在的 NaN
            df_omni_aligned["f10_7_index"] = df_omni_aligned.groupby(df_omni_aligned.index.date)["f10_7_index"].bfill()

        # --- 准备最终的空间天气指数 numpy 数组 ---
        sw_indices = {}  # 存储最终的 1D numpy 数组

        # 辅助函数：从对齐后的 DataFrame 获取数据，处理 NaN 和缩放
        def get_aligned_data(col_name, fill_value, scale=1, dtype=np.float32):
            """从对齐的DataFrame获取数据, 替换NaN, 应用缩放, 转换类型"""
            if col_name in df_omni_aligned.columns:
                # 乘以缩放因子 (例如 Kp * 10)
                data = df_omni_aligned[col_name].values * scale
                # 将 NaN 替换为指定的填充值，并转换数据类型
                return np.nan_to_num(data, nan=fill_value).astype(dtype)
            else:
                # 如果列不存在，发出警告并返回填充值数组
                logging.warning(f"OMNI 列 '{col_name}' 在最终对齐时未找到。将使用填充值 {fill_value} 填充。")
                return np.full((N_times,), fill_value, dtype=dtype)

        # 提取并处理每个指数 (注意使用正确的列名!)
        sw_indices["Scalar_B"] = get_aligned_data("Scalar_B_nT", OMNI_FLOAT_FILL)
        sw_indices["SW_Proton_Density"] = get_aligned_data("SW_Proton_Density_N_cm_3", OMNI_FLOAT_FILL)
        # Kp 指数在OMNI数据中已经是正确的整数格式，无需额外缩放
        sw_indices["Kp_Index"] = get_aligned_data("Kp_index", OMNI_KP_AP_FILL, scale=1, dtype=np.int16)
        sw_indices["Dst_Index"] = get_aligned_data("Dst_index_nT", OMNI_DST_AE_FILL, dtype=np.int16)
        sw_indices["ap_Index"] = get_aligned_data("ap_index_nT", OMNI_KP_AP_FILL, dtype=np.int16)
        sw_indices["F107_Index"] = get_aligned_data("f10_7_index", OMNI_FLOAT_FILL)
        sw_indices["AE_Index"] = get_aligned_data("AE_index_nT", OMNI_DST_AE_FILL, dtype=np.int16)
        logging.info("OMNI 数据对齐并准备完毕。")
    except Exception as e:
        logging.error(f"OMNI 数据对齐过程中失败: {e}")
        return

    # 6. 创建新的 HDF5 文件和内部结构
    logging.info(f"创建新的 HDF5 文件: {OUTPUT_HDF5}")
    try:
        # 使用 h5py 打开文件（写入模式 'w'）
        with h5py.File(OUTPUT_HDF5, "w") as f:
            # --- 设置全局属性 ---
            f.attrs["title"] = "高分辨率中国区域电离层与2小时空间天气指数数据集 (含详细时间, AI就绪)"
            f.attrs["dataset_id"] = f"CRIM_SW2hr_AI_v1.2_{START_YEAR}-{END_YEAR}_DataDrivenRange"
            f.attrs["dataset_version"] = "1.2"
            f.attrs["date_created"] = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            f.attrs["time_coverage_start"] = target_timestamps[0].strftime("%Y-%m-%dT%H:%M:%SZ")
            f.attrs["time_coverage_end"] = target_timestamps[-1].strftime("%Y-%m-%dT%H:%M:%SZ")
            f.attrs["geospatial_lat_min"] = unique_lats.min()
            f.attrs["geospatial_lat_max"] = unique_lats.max()
            f.attrs["geospatial_lon_min"] = unique_lons.min()
            f.attrs["geospatial_lon_max"] = unique_lons.max()
            f.attrs["time_resolution"] = "2 hours (00, 02, ..., 22 UTC)"
            # 添加其他全局属性... (institution, creator, license, keywords, summary 等)
            f.attrs["Conventions"] = "CF-1.8"
            f.attrs["source_TEC"] = f"Original file: {ORIGINAL_TEC_HDF5.name}"  # 可以更详细
            f.attrs["source_SW_Indices"] = f"NASA/GSFC OMNIWeb Plus omni2 dataset hourly (filtered to 2-hourly from {OMNI_DIR.name})"
            # 可以添加 history 属性记录处理步骤

            # --- 创建组 ---
            coords_grp = f.create_group("coordinates")
            iono_grp = f.create_group("ionosphere")
            sw_grp = f.create_group("space_weather_indices")

            # 7. 写入坐标数据
            logging.info("写入坐标数据...")
            # 写入纬度
            ds_lat = coords_grp.create_dataset("latitude", data=unique_lats.astype(np.float32))
            ds_lat.attrs["units"] = "degrees_north"
            ds_lat.attrs["standard_name"] = "latitude"
            ds_lat.attrs["long_name"] = "Latitude"
            ds_lat.attrs["axis"] = "Y"
            # 写入经度
            ds_lon = coords_grp.create_dataset("longitude", data=unique_lons.astype(np.float32))
            ds_lon.attrs["units"] = "degrees_east"
            ds_lon.attrs["standard_name"] = "longitude"
            ds_lon.attrs["long_name"] = "Longitude"
            ds_lon.attrs["axis"] = "X"

            # 写入主数值时间坐标 (Unix 时间戳)
            epoch = pd.Timestamp("1970-01-01", tz="UTC")  # 定义 Unix 纪元
            time_numeric = (target_timestamps - epoch).total_seconds()  # 计算秒数差
            ds_time = coords_grp.create_dataset("time", data=time_numeric, dtype="float64")
            ds_time.attrs["units"] = "seconds since 1970-01-01 00:00:00 UTC"
            ds_time.attrs["standard_name"] = "time"
            ds_time.attrs["long_name"] = "Time (POSIX Epoch Seconds UTC)"
            ds_time.attrs["axis"] = "T"
            ds_time.attrs["calendar"] = "proleptic_gregorian"

            # 写入人类可读的 UTC 时间字符串
            time_strings = [dt.strftime("%Y-%m-%dT%H:%M:%SZ") for dt in target_timestamps]
            string_dt = h5py.string_dtype("ascii", 20)  # 定义固定长度 ASCII 字符串类型
            ds_dt_utc = coords_grp.create_dataset("datetime_utc", data=np.array(time_strings, dtype=string_dt))
            ds_dt_utc.attrs["long_name"] = "UTC Datetime String (ISO 8601 Format)"
            ds_dt_utc.attrs["format"] = "YYYY-MM-DDTHH:MM:SSZ"

            # 写入时间分量
            ds_yr = coords_grp.create_dataset("year", data=target_timestamps.year.astype(np.int16))
            ds_yr.attrs["units"] = "yr"
            ds_yr.attrs["long_name"] = "Year"
            ds_mo = coords_grp.create_dataset("month", data=target_timestamps.month.astype(np.int8))
            ds_mo.attrs["units"] = "mo"
            ds_mo.attrs["long_name"] = "Month (1-12)"
            ds_day = coords_grp.create_dataset("day", data=target_timestamps.day.astype(np.int8))
            ds_day.attrs["units"] = "d"
            ds_day.attrs["long_name"] = "Day of month (1-31)"
            ds_hr = coords_grp.create_dataset("hour", data=target_timestamps.hour.astype(np.int8))
            ds_hr.attrs["units"] = "hr"
            ds_hr.attrs["long_name"] = "Hour of day (0, 2, ..., 22 UTC)"
            ds_doy = coords_grp.create_dataset("day_of_year", data=target_timestamps.dayofyear.astype(np.int16))
            ds_doy.attrs["units"] = "d"
            ds_doy.attrs["long_name"] = "Day of year (1-366)"

            # 8. 重塑并写入 TEC/RMSE 数据
            logging.info("重塑并写入 TEC/RMSE 数据 (这可能需要一些时间)...")
            # --- 初始化 3D 数组 (用填充值填充) ---
            tec_3d = np.full((N_times, N_LAT, N_LON), TEC_FILL_VALUE, dtype=np.float32)
            rmse_3d = np.full((N_times, N_LAT, N_LON), RMSE_FILL_VALUE, dtype=np.float32)

            # --- 创建高效的查找映射 (字典) ---
            # 将目标时间戳列表转换为 字典 {时间戳: 索引} 以快速查找
            target_ts_map = {ts: idx for idx, ts in enumerate(target_timestamps)}
            # 纬度值到索引的映射
            lat_map = {val: idx for idx, val in enumerate(unique_lats)}
            # 经度值到索引的映射
            lon_map = {val: idx for idx, val in enumerate(unique_lons)}
            skipped_count = 0  # 记录无法映射的数据点数量

            # --- 遍历原始 1D 数据，将其映射到新的 3D 网格 ---
            for i in range(len(tec_1d)):
                current_dt_orig = original_datetimes_utc.iloc[i]  # 获取原始时间戳
                if pd.isna(current_dt_orig):  # 跳过无效的原始时间戳
                    skipped_count += 1
                    continue

                current_lat = lat_1d[i]  # 获取原始纬度
                current_lon = lon_1d[i]  # 获取原始经度

                # 在目标时间戳映射中查找当前原始时间戳对应的索引
                t_idx = target_ts_map.get(current_dt_orig)
                # 查找纬度和经度对应的索引
                lat_idx = lat_map.get(current_lat)
                lon_idx = lon_map.get(current_lon)

                # 如果时间、纬度、经度索引都找到了 (即原始数据点有效且在目标网格内)
                if t_idx is not None and lat_idx is not None and lon_idx is not None:
                    # （可选但安全）再次检查索引是否在界限内
                    if 0 <= t_idx < N_times and 0 <= lat_idx < N_LAT and 0 <= lon_idx < N_LON:
                        # 将 1D 数据放入 3D 数组的对应位置
                        tec_3d[t_idx, lat_idx, lon_idx] = tec_1d[i]
                        rmse_3d[t_idx, lat_idx, lon_idx] = rmse_1d[i]
                    else:
                        skipped_count += 1  # 索引越界？不太可能发生，但以防万一
                else:
                    # 如果时间戳或坐标在目标网格/唯一坐标中找不到，则跳过
                    skipped_count += 1

            # 如果有数据点被跳过，发出警告
            if skipped_count > 0:
                logging.warning(f"在映射过程中跳过了 {skipped_count} 个原始数据点 (时间戳/坐标不匹配或原始数据无效)。")

            # --- 写入 TEC 数据集 ---
            # 创建数据集时指定形状、类型、分块、压缩和填充值
            ds_tec = iono_grp.create_dataset(
                "TEC",
                shape=(N_times, N_LAT, N_LON),
                dtype=np.float32,
                chunks=(12, 41, 71),  # 核心分块策略
                compression=COMPRESSION_TYPE,
                compression_opts=COMPRESSION_OPTS,
                fillvalue=TEC_FILL_VALUE,
            )
            ds_tec[:] = tec_3d  # 将准备好的 3D 数据写入数据集
            # 添加 TEC 数据集的属性
            ds_tec.attrs["units"] = "TECU"
            ds_tec.attrs["standard_name"] = "vertical_total_electron_content"
            ds_tec.attrs["long_name"] = "Vertical Total Electron Content"
            ds_tec.attrs["coordinates"] = "/coordinates/time /coordinates/latitude /coordinates/longitude"
            ds_tec.attrs["_FillValue"] = TEC_FILL_VALUE

            # --- 写入 RMSE 数据集 ---
            ds_rmse = iono_grp.create_dataset(
                "RMSE",
                shape=(N_times, N_LAT, N_LON),
                dtype=np.float32,
                chunks=(12, 41, 71),
                compression=COMPRESSION_TYPE,
                compression_opts=COMPRESSION_OPTS,
                fillvalue=RMSE_FILL_VALUE,
            )
            ds_rmse[:] = rmse_3d
            # 添加 RMSE 数据集的属性
            ds_rmse.attrs["units"] = "TECU"
            ds_rmse.attrs["long_name"] = "Root Mean Square Error of TEC estimation"
            ds_rmse.attrs["coordinates"] = "/coordinates/time /coordinates/latitude /coordinates/longitude"
            ds_rmse.attrs["_FillValue"] = RMSE_FILL_VALUE

            # 9. 写入对齐后的空间天气指数数据
            logging.info("写入对齐后的空间天气指数数据...")
            # 遍历准备好的指数数据字典
            for name, data in sw_indices.items():
                ds_sw = sw_grp.create_dataset(name, data=data)
                # 添加通用属性
                ds_sw.attrs["coordinates"] = "/coordinates/time"
                ds_sw.attrs["source"] = "OMNIWeb omni2 (filtered to 2-hourly)"
                # 添加特定属性 (可以做得更精细)
                if name == "Scalar_B":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["long_name"] = "Scalar IMF B (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_FLOAT_FILL
                elif name == "SW_Proton_Density":
                    ds_sw.attrs["units"] = "n/cc"
                    ds_sw.attrs["long_name"] = "Solar Wind Proton Density (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_FLOAT_FILL
                elif name == "Kp_Index":
                    ds_sw.attrs["units"] = "1"
                    ds_sw.attrs["scale_factor"] = 0.1
                    ds_sw.attrs["long_name"] = "Planetary K-index (stored as Kp*10, 2-hourly sample)"
                    ds_sw.attrs["comment"] = "Kp index stored as integer (e.g., 30 = Kp 3.0). Multiply by scale_factor to get actual Kp value."
                    ds_sw.attrs["_FillValue"] = OMNI_KP_AP_FILL
                elif name == "Dst_Index":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["standard_name"] = "dst_index"
                    ds_sw.attrs["long_name"] = "Dst Index (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_DST_AE_FILL
                elif name == "ap_Index":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["long_name"] = "Planetary ap Index (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_KP_AP_FILL
                elif name == "F107_Index":
                    ds_sw.attrs["units"] = "sfu"
                    ds_sw.attrs["standard_name"] = "solar_radio_flux_10.7cm"
                    ds_sw.attrs["long_name"] = "Daily F10.7 (value repeated for 2-hourly steps)"
                    ds_sw.attrs["_FillValue"] = OMNI_FLOAT_FILL
                    ds_sw.attrs["comment"] = "1 sfu = 10e-22 W/m^2/Hz."
                elif name == "AE_Index":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["standard_name"] = "ae_index"
                    ds_sw.attrs["long_name"] = "AE Index (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_DST_AE_FILL

            logging.info("所有数据成功写入 HDF5 文件。")

    except Exception as e:
        # 如果在 HDF5 文件写入过程中发生错误
        logging.error(f"写入 HDF5 文件时发生错误: {e}")
        # 可以选择删除可能已部分创建的文件
        if OUTPUT_HDF5.exists():
            # OUTPUT_HDF5.unlink() # 取消注释以在出错时删除文件
            logging.info(f"可能存在部分创建的文件 {OUTPUT_HDF5}。")

    finally:
        # 记录结束时间和总耗时
        end_time = timer.time()
        logging.info(f"数据集创建过程在 {end_time - start_time:.2f} 秒内完成。")


# --- 运行主函数 ---
if __name__ == "__main__":
    main()
