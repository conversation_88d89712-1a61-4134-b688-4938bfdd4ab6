#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空间天气指数验证脚本

验证 HDF5 文件中的空间天气指数数据与原始 OMNI 数据的一致性
"""

import h5py
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SpaceWeatherValidator:
    def __init__(self):
        self.hdf_dir = Path('hdf')
        self.aux_dir = Path('mat/辅助数据')
        
    def load_omni_data(self, year):
        """加载指定年份的 OMNI 数据"""
        year_dir = self.aux_dir / str(year)
        if not year_dir.exists():
            raise FileNotFoundError(f"找不到年份目录: {year_dir}")
        
        # 查找 .lst 和 .fmt 文件
        lst_files = list(year_dir.glob('*.lst'))
        fmt_files = list(year_dir.glob('*.fmt'))
        
        if not lst_files or not fmt_files:
            raise FileNotFoundError(f"在 {year_dir} 中找不到 .lst 或 .fmt 文件")
        
        lst_file = lst_files[0]
        fmt_file = fmt_files[0]
        
        logging.info(f"加载 OMNI 数据: {lst_file}")
        
        # 解析格式文件（简化版本）
        col_names = ['YEAR', 'DOY', 'Hour', 'Kp_index', 'Dst_index_nT', 
                     'ap_index_nT', 'f10_7_index', 'AE_index_nT']
        col_widths = [4, 4, 3, 3, 6, 4, 6, 5]
        
        # 读取数据
        df = pd.read_fwf(lst_file, widths=col_widths, names=col_names, header=None)
        
        # 数据清理
        for col in df.columns:
            if col not in ['YEAR', 'DOY', 'Hour']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 过滤年份
        df = df[df['YEAR'] == year]
        
        # 创建 datetime 索引
        df['datetime'] = pd.to_datetime(df['YEAR'].astype(str) + '-' +
                                       df['DOY'].astype(int).astype(str) + '-' + 
                                       df['Hour'].astype(int).astype(str),
                                       format='%Y-%j-%H', utc=True, errors='coerce')
        df = df.dropna(subset=['datetime'])
        df = df.set_index('datetime')
        df = df.sort_index()
        
        return df
    
    def load_hdf_space_weather(self, year):
        """加载 HDF5 文件中的空间天气数据"""
        hdf_file = self.hdf_dir / f'CRIM_SW2hr_AI_v1.2_{year}_DataDrivenRange_CN.hdf5'
        if not hdf_file.exists():
            raise FileNotFoundError(f"找不到文件: {hdf_file}")
        
        logging.info(f"加载 HDF5 空间天气数据: {hdf_file}")
        
        with h5py.File(hdf_file, 'r') as f:
            # 读取时间坐标
            datetime_utc = [dt.decode() for dt in f['coordinates/datetime_utc'][:]]
            timestamps = pd.to_datetime(datetime_utc, utc=True)
            
            # 读取空间天气指数
            sw_data = {}
            sw_group = f['space_weather_indices']
            for key in sw_group.keys():
                sw_data[key] = sw_group[key][:]
            
            # 创建 DataFrame
            df = pd.DataFrame(sw_data, index=timestamps)
            
        return df
    
    def validate_space_weather_year(self, year):
        """验证指定年份的空间天气数据"""
        logging.info(f"验证 {year} 年的空间天气指数数据")
        
        try:
            # 加载数据
            omni_data = self.load_omni_data(year)
            hdf_data = self.load_hdf_space_weather(year)
            
            validation_result = {
                'year': year,
                'issues': [],
                'statistics': {}
            }
            
            # 检查时间范围
            omni_start = omni_data.index.min()
            omni_end = omni_data.index.max()
            hdf_start = hdf_data.index.min()
            hdf_end = hdf_data.index.max()
            
            logging.info(f"OMNI 时间范围: {omni_start} - {omni_end}")
            logging.info(f"HDF5 时间范围: {hdf_start} - {hdf_end}")
            
            # 对齐到 2 小时网格
            target_timestamps = pd.date_range(start=hdf_start, end=hdf_end, freq='2H', tz='UTC')
            omni_aligned = omni_data.reindex(target_timestamps, method=None)
            
            # 验证各个指数
            self.validate_kp_index(omni_aligned, hdf_data, validation_result)
            self.validate_dst_index(omni_aligned, hdf_data, validation_result)
            self.validate_ap_index(omni_aligned, hdf_data, validation_result)
            self.validate_f107_index(omni_aligned, hdf_data, validation_result)
            self.validate_ae_index(omni_aligned, hdf_data, validation_result)
            
            return validation_result
            
        except Exception as e:
            logging.error(f"验证 {year} 年空间天气数据时出错: {e}")
            return {'year': year, 'issues': [f"验证失败: {str(e)}"], 'statistics': {}}
    
    def validate_kp_index(self, omni_data, hdf_data, result):
        """验证 Kp 指数"""
        if 'Kp_Index' not in hdf_data.columns or 'Kp_index' not in omni_data.columns:
            result['issues'].append("缺少 Kp 指数数据")
            return
        
        # Kp 指数在 HDF5 中被乘以 10
        omni_kp = omni_data['Kp_index'].fillna(999) * 10  # 填充值处理
        hdf_kp = hdf_data['Kp_Index']
        
        # 比较非填充值
        valid_mask = (omni_kp != 9990) & (hdf_kp != 999)
        if valid_mask.sum() > 0:
            omni_valid = omni_kp[valid_mask]
            hdf_valid = hdf_kp[valid_mask]
            
            matches = np.isclose(omni_valid, hdf_valid, atol=1)
            match_rate = matches.sum() / len(matches)
            
            result['statistics']['Kp_Index'] = {
                'total_comparisons': len(matches),
                'matches': matches.sum(),
                'match_rate': match_rate
            }
            
            if match_rate < 0.95:
                result['issues'].append(f"Kp 指数匹配率过低: {match_rate:.2%}")
        else:
            result['issues'].append("没有有效的 Kp 指数数据进行比较")
    
    def validate_dst_index(self, omni_data, hdf_data, result):
        """验证 Dst 指数"""
        if 'Dst_Index' not in hdf_data.columns or 'Dst_index_nT' not in omni_data.columns:
            result['issues'].append("缺少 Dst 指数数据")
            return
        
        omni_dst = omni_data['Dst_index_nT'].fillna(99999)
        hdf_dst = hdf_data['Dst_Index']
        
        # 比较非填充值
        valid_mask = (omni_dst != 99999) & (hdf_dst != 99999)
        if valid_mask.sum() > 0:
            omni_valid = omni_dst[valid_mask]
            hdf_valid = hdf_dst[valid_mask]
            
            matches = np.isclose(omni_valid, hdf_valid, atol=1)
            match_rate = matches.sum() / len(matches)
            
            result['statistics']['Dst_Index'] = {
                'total_comparisons': len(matches),
                'matches': matches.sum(),
                'match_rate': match_rate
            }
            
            if match_rate < 0.95:
                result['issues'].append(f"Dst 指数匹配率过低: {match_rate:.2%}")
        else:
            result['issues'].append("没有有效的 Dst 指数数据进行比较")
    
    def validate_ap_index(self, omni_data, hdf_data, result):
        """验证 ap 指数"""
        if 'ap_Index' not in hdf_data.columns or 'ap_index_nT' not in omni_data.columns:
            result['issues'].append("缺少 ap 指数数据")
            return
        
        omni_ap = omni_data['ap_index_nT'].fillna(999)
        hdf_ap = hdf_data['ap_Index']
        
        # 比较非填充值
        valid_mask = (omni_ap != 999) & (hdf_ap != 999)
        if valid_mask.sum() > 0:
            omni_valid = omni_ap[valid_mask]
            hdf_valid = hdf_ap[valid_mask]
            
            matches = np.isclose(omni_valid, hdf_valid, atol=1)
            match_rate = matches.sum() / len(matches)
            
            result['statistics']['ap_Index'] = {
                'total_comparisons': len(matches),
                'matches': matches.sum(),
                'match_rate': match_rate
            }
            
            if match_rate < 0.95:
                result['issues'].append(f"ap 指数匹配率过低: {match_rate:.2%}")
        else:
            result['issues'].append("没有有效的 ap 指数数据进行比较")
    
    def validate_f107_index(self, omni_data, hdf_data, result):
        """验证 F10.7 指数"""
        if 'F107_Index' not in hdf_data.columns or 'f10_7_index' not in omni_data.columns:
            result['issues'].append("缺少 F10.7 指数数据")
            return
        
        omni_f107 = omni_data['f10_7_index'].fillna(999.9)
        hdf_f107 = hdf_data['F107_Index']
        
        # 比较非填充值
        valid_mask = (omni_f107 != 999.9) & (hdf_f107 != 999.9)
        if valid_mask.sum() > 0:
            omni_valid = omni_f107[valid_mask]
            hdf_valid = hdf_f107[valid_mask]
            
            matches = np.isclose(omni_valid, hdf_valid, rtol=0.01)
            match_rate = matches.sum() / len(matches)
            
            result['statistics']['F107_Index'] = {
                'total_comparisons': len(matches),
                'matches': matches.sum(),
                'match_rate': match_rate
            }
            
            if match_rate < 0.95:
                result['issues'].append(f"F10.7 指数匹配率过低: {match_rate:.2%}")
        else:
            result['issues'].append("没有有效的 F10.7 指数数据进行比较")
    
    def validate_ae_index(self, omni_data, hdf_data, result):
        """验证 AE 指数"""
        if 'AE_Index' not in hdf_data.columns or 'AE_index_nT' not in omni_data.columns:
            result['issues'].append("缺少 AE 指数数据")
            return
        
        omni_ae = omni_data['AE_index_nT'].fillna(99999)
        hdf_ae = hdf_data['AE_Index']
        
        # 比较非填充值
        valid_mask = (omni_ae != 99999) & (hdf_ae != 99999)
        if valid_mask.sum() > 0:
            omni_valid = omni_ae[valid_mask]
            hdf_valid = hdf_ae[valid_mask]
            
            matches = np.isclose(omni_valid, hdf_valid, atol=1)
            match_rate = matches.sum() / len(matches)
            
            result['statistics']['AE_Index'] = {
                'total_comparisons': len(matches),
                'matches': matches.sum(),
                'match_rate': match_rate
            }
            
            if match_rate < 0.95:
                result['issues'].append(f"AE 指数匹配率过低: {match_rate:.2%}")
        else:
            result['issues'].append("没有有效的 AE 指数数据进行比较")
    
    def run_validation(self, years=None):
        """运行空间天气指数验证"""
        if years is None:
            years = [2020, 2021, 2022]  # 选择几个年份进行验证
        
        logging.info(f"开始验证空间天气指数，年份: {years}")
        
        results = []
        for year in years:
            result = self.validate_space_weather_year(year)
            results.append(result)
        
        return results
    
    def generate_report(self, results):
        """生成验证报告"""
        report = []
        report.append("=" * 80)
        report.append("空间天气指数验证报告")
        report.append("=" * 80)
        
        total_validations = len(results)
        successful_validations = sum(1 for r in results if not r['issues'])
        
        report.append(f"\n总体统计:")
        report.append(f"  验证的年份: {total_validations}")
        report.append(f"  成功验证: {successful_validations}")
        report.append(f"  发现问题: {total_validations - successful_validations}")
        report.append(f"  成功率: {successful_validations/total_validations*100:.1f}%")
        
        for result in results:
            year = result['year']
            report.append(f"\n{year} 年验证结果:")
            
            if result['issues']:
                report.append(f"  发现 {len(result['issues'])} 个问题:")
                for issue in result['issues']:
                    report.append(f"    - {issue}")
            else:
                report.append(f"  ✓ 验证通过")
            
            # 显示统计信息
            if result['statistics']:
                report.append(f"  指数匹配统计:")
                for index_name, stats in result['statistics'].items():
                    match_rate = stats['match_rate'] * 100
                    report.append(f"    {index_name}: {stats['matches']}/{stats['total_comparisons']} "
                                f"匹配, 匹配率: {match_rate:.1f}%")
        
        return "\n".join(report)

if __name__ == "__main__":
    validator = SpaceWeatherValidator()
    
    # 运行验证（选择几个年份）
    results = validator.run_validation(years=[2020, 2021, 2022])
    
    # 生成并打印报告
    report = validator.generate_report(results)
    print(report)
    
    # 保存报告到文件
    with open('space_weather_validation_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    logging.info("空间天气指数验证完成，报告已保存到 space_weather_validation_report.txt")
