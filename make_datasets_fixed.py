#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版数据处理脚本

从 mat/ 目录的年度 .mat 文件生成 HDF5 文件，修复了 Kp 指数的缩放问题
"""

import h5py
import numpy as np
import pandas as pd
import datetime
import logging
import scipy.io
from pathlib import Path
import time as timer
import argparse

# --- 配置参数 ---
MAT_DIR = Path("mat")
AUX_DIR = Path("mat/辅助数据")
HDF_DIR = Path("hdf")

# 网格维度
N_LAT = 41
N_LON = 71

# 目标时间分辨率 (0, 2, ..., 22 UTC)
TARGET_HOURS = list(range(0, 24, 2))

# 填充值
TEC_FILL_VALUE = -9999.0
RMSE_FILL_VALUE = -9999.0
OMNI_FLOAT_FILL = 999.9
OMNI_DST_AE_FILL = 99999
OMNI_KP_AP_FILL = 999

# HDF5 压缩设置
COMPRESSION_TYPE = "gzip"
COMPRESSION_OPTS = 4

# 日志设置
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def load_mat_data(year):
    """加载指定年份的 .mat 文件数据"""
    mat_file = MAT_DIR / f"{year}.mat"
    if not mat_file.exists():
        raise FileNotFoundError(f"找不到文件: {mat_file}")

    logging.info(f"加载 MAT 文件: {mat_file}")
    data = scipy.io.loadmat(mat_file)

    if "A" not in data:
        raise KeyError(f"MAT 文件 {mat_file} 中找不到数组 'A'")

    A = data["A"]
    logging.info(f"MAT 数据形状: {A.shape}")

    # 解析数据列
    mat_data = {
        "year": A[:, 0].astype(int),
        "doy": A[:, 1].astype(int),
        "hour": A[:, 2].astype(int),
        "latitude": A[:, 3],
        "longitude": A[:, 4],
        "tec": A[:, 5],
        "rmse": A[:, 6],
    }

    return mat_data


def load_omni_data(year):
    """加载指定年份的 OMNI 数据"""
    year_dir = AUX_DIR / str(year)
    if not year_dir.exists():
        logging.warning(f"找不到年份目录: {year_dir}，跳过 OMNI 数据")
        return None

    # 查找 .lst 文件
    lst_files = list(year_dir.glob("*.lst"))
    if not lst_files:
        logging.warning(f"在 {year_dir} 中找不到 .lst 文件，跳过 OMNI 数据")
        return None

    lst_file = lst_files[0]
    logging.info(f"加载 OMNI 数据: {lst_file}")

    # 根据实际的 OMNI 格式定义列名和列宽
    col_names = ["YEAR", "DOY", "Hour", "Kp_index", "Dst_index_nT", "ap_index_nT", "f10_7_index", "AE_index_nT"]
    col_widths = [4, 4, 3, 3, 6, 4, 6, 5]

    # 读取数据
    df = pd.read_fwf(lst_file, widths=col_widths, names=col_names, header=None)

    # 数据清理
    for col in df.columns:
        if col not in ["YEAR", "DOY", "Hour"]:
            df[col] = pd.to_numeric(df[col], errors="coerce")

    # 过滤年份
    df = df[df["YEAR"] == year]

    if df.empty:
        logging.warning(f"OMNI 文件中没有 {year} 年的数据")
        return None

    # 创建 datetime 索引
    df["datetime"] = pd.to_datetime(
        df["YEAR"].astype(str) + "-" + df["DOY"].astype(int).astype(str) + "-" + df["Hour"].astype(int).astype(str),
        format="%Y-%j-%H",
        utc=True,
        errors="coerce",
    )
    df = df.dropna(subset=["datetime"])
    df = df.set_index("datetime")
    df = df.sort_index()

    return df


def process_year(year, force_overwrite=False):
    """处理指定年份的数据"""
    start_time = timer.time()
    logging.info(f"开始处理 {year} 年的数据...")

    # 检查输出文件是否已存在
    output_file = HDF_DIR / f"CRIM_SW2hr_AI_v1.2_{year}_DataDrivenRange_CN.hdf5"
    if output_file.exists() and not force_overwrite:
        logging.info(f"输出文件 {output_file} 已存在，跳过处理（使用 --force 强制覆盖）")
        return

    try:
        # 1. 加载 MAT 数据
        mat_data = load_mat_data(year)

        # 2. 加载 OMNI 数据
        omni_data = load_omni_data(year)

        # 3. 处理时间和坐标
        logging.info("处理时间和坐标...")

        # 创建 datetime 对象
        datetimes = []
        for i in range(len(mat_data["year"])):
            try:
                year = int(mat_data["year"][i])
                doy = int(mat_data["doy"][i])
                hour = int(mat_data["hour"][i])

                # 验证数据范围
                if not (1 <= doy <= 366):
                    datetimes.append(pd.NaT)
                    continue
                if not (0 <= hour <= 23):
                    datetimes.append(pd.NaT)
                    continue

                dt = datetime.datetime(year, 1, 1, hour, 0, 0, tzinfo=datetime.timezone.utc) + datetime.timedelta(days=doy - 1)
                datetimes.append(dt)
            except Exception as e:
                if i < 10:  # 只打印前10个错误
                    logging.warning(f"时间转换失败 (索引 {i}): {e}")
                datetimes.append(pd.NaT)

        datetimes = pd.Series(datetimes)

        # 确定时间范围
        valid_datetimes = datetimes.dropna()
        if valid_datetimes.empty:
            raise ValueError("没有有效的时间数据")

        start_date = valid_datetimes.min().replace(hour=0, minute=0, second=0)
        end_date = valid_datetimes.max().replace(hour=22, minute=0, second=0)

        # 生成目标时间网格
        target_timestamps = pd.date_range(start=start_date, end=end_date, freq="2h", tz="UTC")
        N_times = len(target_timestamps)

        logging.info(f"时间范围: {start_date} - {end_date}")
        logging.info(f"目标时间点数: {N_times}")

        # 获取唯一坐标
        unique_lats = np.sort(np.unique(mat_data["latitude"]))
        unique_lons = np.sort(np.unique(mat_data["longitude"]))

        logging.info(f"纬度范围: {unique_lats.min():.1f} - {unique_lats.max():.1f} ({len(unique_lats)} 个点)")
        logging.info(f"经度范围: {unique_lons.min():.1f} - {unique_lons.max():.1f} ({len(unique_lons)} 个点)")

        # 4. 处理空间天气指数
        sw_indices = {}
        if omni_data is not None:
            logging.info("处理空间天气指数...")

            # 对齐到目标时间网格
            omni_aligned = omni_data.reindex(target_timestamps, method=None)

            # 处理 F10.7 (日值指数)
            if "f10_7_index" in omni_aligned.columns:
                omni_aligned["f10_7_index"] = omni_aligned.groupby(omni_aligned.index.date)["f10_7_index"].ffill()
                omni_aligned["f10_7_index"] = omni_aligned.groupby(omni_aligned.index.date)["f10_7_index"].bfill()

            # 提取各个指数（修复了 Kp 指数的缩放问题）
            def get_aligned_data(col_name, fill_value, scale=1, dtype=np.float32):
                if col_name in omni_aligned.columns:
                    data = omni_aligned[col_name].values * scale
                    return np.nan_to_num(data, nan=fill_value).astype(dtype)
                else:
                    logging.warning(f"OMNI 列 '{col_name}' 未找到，使用填充值")
                    return np.full((N_times,), fill_value, dtype=dtype)

            # 注意：Kp 指数不再乘以 10，因为 OMNI 数据中已经是正确格式
            sw_indices["Kp_Index"] = get_aligned_data("Kp_index", OMNI_KP_AP_FILL, scale=1, dtype=np.int16)
            sw_indices["Dst_Index"] = get_aligned_data("Dst_index_nT", OMNI_DST_AE_FILL, dtype=np.int16)
            sw_indices["ap_Index"] = get_aligned_data("ap_index_nT", OMNI_KP_AP_FILL, dtype=np.int16)
            sw_indices["F107_Index"] = get_aligned_data("f10_7_index", OMNI_FLOAT_FILL)
            sw_indices["AE_Index"] = get_aligned_data("AE_index_nT", OMNI_DST_AE_FILL, dtype=np.int16)
        else:
            logging.warning("没有 OMNI 数据，空间天气指数将使用填充值")
            sw_indices["Kp_Index"] = np.full((N_times,), OMNI_KP_AP_FILL, dtype=np.int16)
            sw_indices["Dst_Index"] = np.full((N_times,), OMNI_DST_AE_FILL, dtype=np.int16)
            sw_indices["ap_Index"] = np.full((N_times,), OMNI_KP_AP_FILL, dtype=np.int16)
            sw_indices["F107_Index"] = np.full((N_times,), OMNI_FLOAT_FILL, dtype=np.float32)
            sw_indices["AE_Index"] = np.full((N_times,), OMNI_DST_AE_FILL, dtype=np.int16)

        # 5. 重塑 TEC/RMSE 数据
        logging.info("重塑 TEC/RMSE 数据...")

        # 初始化 3D 数组
        tec_3d = np.full((N_times, N_LAT, N_LON), TEC_FILL_VALUE, dtype=np.float32)
        rmse_3d = np.full((N_times, N_LAT, N_LON), RMSE_FILL_VALUE, dtype=np.float32)

        # 创建映射
        target_ts_map = {ts: idx for idx, ts in enumerate(target_timestamps)}
        lat_map = {val: idx for idx, val in enumerate(unique_lats)}
        lon_map = {val: idx for idx, val in enumerate(unique_lons)}

        # 映射数据
        skipped_count = 0
        for i in range(len(mat_data["tec"])):
            dt = datetimes.iloc[i]
            if pd.isna(dt):
                skipped_count += 1
                continue

            t_idx = target_ts_map.get(dt)
            lat_idx = lat_map.get(mat_data["latitude"][i])
            lon_idx = lon_map.get(mat_data["longitude"][i])

            if t_idx is not None and lat_idx is not None and lon_idx is not None:
                if 0 <= t_idx < N_times and 0 <= lat_idx < N_LAT and 0 <= lon_idx < N_LON:
                    tec_3d[t_idx, lat_idx, lon_idx] = mat_data["tec"][i]
                    rmse_3d[t_idx, lat_idx, lon_idx] = mat_data["rmse"][i]
                else:
                    skipped_count += 1
            else:
                skipped_count += 1

        if skipped_count > 0:
            logging.warning(f"跳过了 {skipped_count} 个数据点")

        # 6. 写入 HDF5 文件
        logging.info(f"写入 HDF5 文件: {output_file}")

        # 确保输出目录存在
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with h5py.File(output_file, "w") as f:
            # 全局属性
            f.attrs["title"] = "高分辨率中国区域电离层与2小时空间天气指数数据集 (含详细时间, AI就绪)"
            f.attrs["dataset_id"] = f"CRIM_SW2hr_AI_v1.2_{year}_DataDrivenRange"
            f.attrs["dataset_version"] = "1.2"
            f.attrs["date_created"] = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            f.attrs["time_coverage_start"] = target_timestamps[0].strftime("%Y-%m-%dT%H:%M:%SZ")
            f.attrs["time_coverage_end"] = target_timestamps[-1].strftime("%Y-%m-%dT%H:%M:%SZ")
            f.attrs["geospatial_lat_min"] = unique_lats.min()
            f.attrs["geospatial_lat_max"] = unique_lats.max()
            f.attrs["geospatial_lon_min"] = unique_lons.min()
            f.attrs["geospatial_lon_max"] = unique_lons.max()
            f.attrs["time_resolution"] = "2 hours (00, 02, ..., 22 UTC)"
            f.attrs["Conventions"] = "CF-1.8"
            f.attrs["source_TEC"] = f"Original file: {year}.mat"
            f.attrs["source_SW_Indices"] = f"NASA/GSFC OMNIWeb Plus omni2 dataset hourly (filtered to 2-hourly)"

            # 创建组
            coords_grp = f.create_group("coordinates")
            iono_grp = f.create_group("ionosphere")
            sw_grp = f.create_group("space_weather_indices")

            # 写入坐标
            ds_lat = coords_grp.create_dataset("latitude", data=unique_lats.astype(np.float32))
            ds_lat.attrs["units"] = "degrees_north"
            ds_lat.attrs["standard_name"] = "latitude"
            ds_lat.attrs["long_name"] = "Latitude"
            ds_lat.attrs["axis"] = "Y"

            ds_lon = coords_grp.create_dataset("longitude", data=unique_lons.astype(np.float32))
            ds_lon.attrs["units"] = "degrees_east"
            ds_lon.attrs["standard_name"] = "longitude"
            ds_lon.attrs["long_name"] = "Longitude"
            ds_lon.attrs["axis"] = "X"

            # 时间坐标
            epoch = pd.Timestamp("1970-01-01", tz="UTC")
            time_numeric = (target_timestamps - epoch).total_seconds()
            ds_time = coords_grp.create_dataset("time", data=time_numeric, dtype="float64")
            ds_time.attrs["units"] = "seconds since 1970-01-01 00:00:00 UTC"
            ds_time.attrs["standard_name"] = "time"
            ds_time.attrs["long_name"] = "Time (POSIX Epoch Seconds UTC)"
            ds_time.attrs["axis"] = "T"
            ds_time.attrs["calendar"] = "proleptic_gregorian"

            # 时间字符串
            time_strings = [dt.strftime("%Y-%m-%dT%H:%M:%SZ") for dt in target_timestamps]
            string_dt = h5py.string_dtype("ascii", 20)
            ds_dt_utc = coords_grp.create_dataset("datetime_utc", data=np.array(time_strings, dtype=string_dt))
            ds_dt_utc.attrs["long_name"] = "UTC Datetime String (ISO 8601 Format)"
            ds_dt_utc.attrs["format"] = "YYYY-MM-DDTHH:MM:SSZ"

            # 时间分量
            coords_grp.create_dataset("year", data=target_timestamps.year.astype(np.int16))
            coords_grp.create_dataset("month", data=target_timestamps.month.astype(np.int8))
            coords_grp.create_dataset("day", data=target_timestamps.day.astype(np.int8))
            coords_grp.create_dataset("hour", data=target_timestamps.hour.astype(np.int8))
            coords_grp.create_dataset("day_of_year", data=target_timestamps.dayofyear.astype(np.int16))

            # 写入 TEC 数据
            ds_tec = iono_grp.create_dataset(
                "TEC",
                shape=(N_times, N_LAT, N_LON),
                dtype=np.float32,
                chunks=(12, 41, 71),
                compression=COMPRESSION_TYPE,
                compression_opts=COMPRESSION_OPTS,
                fillvalue=TEC_FILL_VALUE,
            )
            ds_tec[:] = tec_3d
            ds_tec.attrs["units"] = "TECU"
            ds_tec.attrs["standard_name"] = "vertical_total_electron_content"
            ds_tec.attrs["long_name"] = "Vertical Total Electron Content"
            ds_tec.attrs["coordinates"] = "/coordinates/time /coordinates/latitude /coordinates/longitude"
            ds_tec.attrs["_FillValue"] = TEC_FILL_VALUE

            # 写入 RMSE 数据
            ds_rmse = iono_grp.create_dataset(
                "RMSE",
                shape=(N_times, N_LAT, N_LON),
                dtype=np.float32,
                chunks=(12, 41, 71),
                compression=COMPRESSION_TYPE,
                compression_opts=COMPRESSION_OPTS,
                fillvalue=RMSE_FILL_VALUE,
            )
            ds_rmse[:] = rmse_3d
            ds_rmse.attrs["units"] = "TECU"
            ds_rmse.attrs["long_name"] = "Root Mean Square Error of TEC estimation"
            ds_rmse.attrs["coordinates"] = "/coordinates/time /coordinates/latitude /coordinates/longitude"
            ds_rmse.attrs["_FillValue"] = RMSE_FILL_VALUE

            # 写入空间天气指数
            for name, data in sw_indices.items():
                ds_sw = sw_grp.create_dataset(name, data=data)
                ds_sw.attrs["coordinates"] = "/coordinates/time"
                ds_sw.attrs["source"] = "OMNIWeb omni2 (filtered to 2-hourly)"

                if name == "Kp_Index":
                    ds_sw.attrs["units"] = "1"
                    ds_sw.attrs["scale_factor"] = 0.1
                    ds_sw.attrs["long_name"] = "Planetary K-index (stored as Kp*10, 2-hourly sample)"
                    ds_sw.attrs["comment"] = "Kp index stored as integer (e.g., 30 = Kp 3.0). Multiply by scale_factor to get actual Kp value."
                    ds_sw.attrs["_FillValue"] = OMNI_KP_AP_FILL
                elif name == "Dst_Index":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["standard_name"] = "dst_index"
                    ds_sw.attrs["long_name"] = "Dst Index (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_DST_AE_FILL
                elif name == "ap_Index":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["long_name"] = "Planetary ap Index (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_KP_AP_FILL
                elif name == "F107_Index":
                    ds_sw.attrs["units"] = "sfu"
                    ds_sw.attrs["standard_name"] = "solar_radio_flux_10.7cm"
                    ds_sw.attrs["long_name"] = "Daily F10.7 (value repeated for 2-hourly steps)"
                    ds_sw.attrs["_FillValue"] = OMNI_FLOAT_FILL
                    ds_sw.attrs["comment"] = "1 sfu = 10e-22 W/m^2/Hz."
                elif name == "AE_Index":
                    ds_sw.attrs["units"] = "nT"
                    ds_sw.attrs["standard_name"] = "ae_index"
                    ds_sw.attrs["long_name"] = "AE Index (2-hourly sample)"
                    ds_sw.attrs["_FillValue"] = OMNI_DST_AE_FILL

        end_time = timer.time()
        logging.info(f"{year} 年数据处理完成，耗时 {end_time - start_time:.2f} 秒")

    except Exception as e:
        logging.error(f"处理 {year} 年数据时出错: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description="生成修复版 HDF5 数据文件")
    parser.add_argument("--years", nargs="+", type=int, help="要处理的年份列表")
    parser.add_argument("--all", action="store_true", help="处理所有可用年份")
    parser.add_argument("--force", action="store_true", help="强制覆盖已存在的文件")

    args = parser.parse_args()

    if args.all:
        # 查找所有可用的年份
        mat_files = list(MAT_DIR.glob("*.mat"))
        years = []
        for mat_file in mat_files:
            try:
                year = int(mat_file.stem)
                if 2013 <= year <= 2025:
                    years.append(year)
            except ValueError:
                continue
        years = sorted(years)
    elif args.years:
        years = args.years
    else:
        print("请指定要处理的年份（--years）或使用 --all 处理所有年份")
        return

    logging.info(f"开始处理年份: {years}")

    for year in years:
        try:
            process_year(year, force_overwrite=args.force)
        except Exception as e:
            logging.error(f"处理 {year} 年失败: {e}")
            continue

    logging.info("所有年份处理完成")


if __name__ == "__main__":
    main()
