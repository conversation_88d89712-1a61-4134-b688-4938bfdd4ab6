#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理管道验证脚本

验证从 mat/ 目录的原始数据到 hdf/ 目录的处理数据的转换正确性
"""

import h5py
import numpy as np
import pandas as pd
import scipy.io
import datetime
import random
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class DataPipelineValidator:
    def __init__(self):
        self.mat_dir = Path('mat')
        self.hdf_dir = Path('hdf')
        self.aux_dir = Path('mat/辅助数据')
        self.validation_results = []
        
    def get_available_years(self):
        """获取可用的年份列表"""
        mat_files = list(self.mat_dir.glob('*.mat'))
        years = []
        for mat_file in mat_files:
            try:
                year = int(mat_file.stem)
                if 2013 <= year <= 2025:  # 合理的年份范围
                    years.append(year)
            except ValueError:
                continue
        return sorted(years)
    
    def select_random_days(self, year, num_days=3):
        """为指定年份随机选择几天进行验证"""
        # 确定该年份的天数
        is_leap = (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)
        max_day = 366 if is_leap else 365
        
        # 随机选择天数
        selected_days = random.sample(range(1, max_day + 1), min(num_days, max_day))
        return sorted(selected_days)
    
    def load_mat_data(self, year):
        """加载指定年份的 .mat 文件数据"""
        mat_file = self.mat_dir / f'{year}.mat'
        if not mat_file.exists():
            raise FileNotFoundError(f"找不到文件: {mat_file}")
        
        logging.info(f"加载 MAT 文件: {mat_file}")
        data = scipy.io.loadmat(mat_file)
        
        if 'A' not in data:
            raise KeyError(f"MAT 文件 {mat_file} 中找不到数组 'A'")
        
        A = data['A']
        logging.info(f"MAT 数据形状: {A.shape}")
        
        # 解析数据列
        mat_data = {
            'year': A[:, 0].astype(int),
            'doy': A[:, 1].astype(int),
            'hour': A[:, 2].astype(int),
            'latitude': A[:, 3],
            'longitude': A[:, 4],
            'tec': A[:, 5],
            'rmse': A[:, 6]
        }
        
        return mat_data
    
    def load_hdf_data(self, year):
        """加载指定年份的 HDF5 文件数据"""
        hdf_file = self.hdf_dir / f'CRIM_SW2hr_AI_v1.2_{year}_DataDrivenRange_CN.hdf5'
        if not hdf_file.exists():
            raise FileNotFoundError(f"找不到文件: {hdf_file}")
        
        logging.info(f"加载 HDF5 文件: {hdf_file}")
        
        with h5py.File(hdf_file, 'r') as f:
            hdf_data = {
                'time': f['coordinates/time'][:],
                'datetime_utc': [dt.decode() for dt in f['coordinates/datetime_utc'][:]],
                'year': f['coordinates/year'][:],
                'month': f['coordinates/month'][:],
                'day': f['coordinates/day'][:],
                'hour': f['coordinates/hour'][:],
                'day_of_year': f['coordinates/day_of_year'][:],
                'latitude': f['coordinates/latitude'][:],
                'longitude': f['coordinates/longitude'][:],
                'tec': f['ionosphere/TEC'][:],
                'rmse': f['ionosphere/RMSE'][:],
                'tec_fill_value': f['ionosphere/TEC'].attrs.get('_FillValue', -9999.0),
                'rmse_fill_value': f['ionosphere/RMSE'].attrs.get('_FillValue', -9999.0)
            }
        
        logging.info(f"HDF5 数据形状 - TEC: {hdf_data['tec'].shape}, RMSE: {hdf_data['rmse'].shape}")
        return hdf_data
    
    def validate_day_data(self, year, doy, mat_data, hdf_data):
        """验证指定年份和年积日的数据"""
        logging.info(f"验证 {year} 年第 {doy} 天的数据")
        
        validation_result = {
            'year': year,
            'doy': doy,
            'issues': [],
            'statistics': {}
        }
        
        # 从 MAT 数据中提取指定天的数据
        mat_day_mask = (mat_data['year'] == year) & (mat_data['doy'] == doy)
        mat_day_data = {key: val[mat_day_mask] for key, val in mat_data.items()}
        
        if len(mat_day_data['year']) == 0:
            validation_result['issues'].append(f"MAT 文件中没有找到 {year} 年第 {doy} 天的数据")
            return validation_result
        
        # 从 HDF5 数据中提取指定天的数据
        hdf_day_mask = (hdf_data['year'] == year) & (hdf_data['day_of_year'] == doy)
        hdf_day_indices = np.where(hdf_day_mask)[0]
        
        if len(hdf_day_indices) == 0:
            validation_result['issues'].append(f"HDF5 文件中没有找到 {year} 年第 {doy} 天的数据")
            return validation_result
        
        # 验证时间点数量
        expected_hours = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22]
        mat_hours = sorted(np.unique(mat_day_data['hour']))
        hdf_hours = sorted(hdf_data['hour'][hdf_day_indices])
        
        if not np.array_equal(mat_hours, expected_hours):
            validation_result['issues'].append(f"MAT 数据小时不完整: 期望 {expected_hours}, 实际 {mat_hours}")
        
        if not np.array_equal(hdf_hours, expected_hours):
            validation_result['issues'].append(f"HDF5 数据小时不完整: 期望 {expected_hours}, 实际 {hdf_hours}")
        
        # 验证每个时间点的数据
        for hour in expected_hours:
            if hour in mat_hours and hour in hdf_hours:
                self.validate_hourly_data(year, doy, hour, mat_day_data, hdf_data, hdf_day_indices, validation_result)
        
        return validation_result
    
    def validate_hourly_data(self, year, doy, hour, mat_day_data, hdf_data, hdf_day_indices, validation_result):
        """验证指定小时的数据"""
        # 从 MAT 数据中提取指定小时的数据
        mat_hour_mask = mat_day_data['hour'] == hour
        mat_hour_data = {key: val[mat_hour_mask] for key, val in mat_day_data.items()}
        
        # 从 HDF5 数据中找到对应的时间索引
        hdf_hour_mask = hdf_data['hour'][hdf_day_indices] == hour
        hdf_time_idx = hdf_day_indices[hdf_hour_mask]
        
        if len(hdf_time_idx) != 1:
            validation_result['issues'].append(f"第 {doy} 天 {hour} 时的 HDF5 时间索引异常: {len(hdf_time_idx)} 个")
            return
        
        hdf_time_idx = hdf_time_idx[0]
        
        # 获取 HDF5 中该时间点的 2D 数据
        hdf_tec_2d = hdf_data['tec'][hdf_time_idx]
        hdf_rmse_2d = hdf_data['rmse'][hdf_time_idx]
        
        # 验证数据点数量
        expected_points = len(hdf_data['latitude']) * len(hdf_data['longitude'])  # 41 * 71 = 2911
        actual_mat_points = len(mat_hour_data['tec'])
        
        if actual_mat_points != expected_points:
            validation_result['issues'].append(
                f"第 {doy} 天 {hour} 时 MAT 数据点数量异常: 期望 {expected_points}, 实际 {actual_mat_points}"
            )
        
        # 逐点比较数据
        mismatches = 0
        total_comparisons = 0
        
        for i in range(len(mat_hour_data['tec'])):
            mat_lat = mat_hour_data['latitude'][i]
            mat_lon = mat_hour_data['longitude'][i]
            mat_tec = mat_hour_data['tec'][i]
            mat_rmse = mat_hour_data['rmse'][i]
            
            # 在 HDF5 坐标中找到对应的索引
            lat_idx = np.where(np.abs(hdf_data['latitude'] - mat_lat) < 0.001)[0]
            lon_idx = np.where(np.abs(hdf_data['longitude'] - mat_lon) < 0.001)[0]
            
            if len(lat_idx) == 1 and len(lon_idx) == 1:
                lat_idx, lon_idx = lat_idx[0], lon_idx[0]
                hdf_tec = hdf_tec_2d[lat_idx, lon_idx]
                hdf_rmse = hdf_rmse_2d[lat_idx, lon_idx]
                
                # 比较 TEC 值
                if not np.isclose(mat_tec, hdf_tec, rtol=1e-5, atol=1e-6):
                    mismatches += 1
                    if mismatches <= 5:  # 只记录前5个不匹配的例子
                        validation_result['issues'].append(
                            f"第 {doy} 天 {hour} 时 TEC 不匹配 (lat={mat_lat}, lon={mat_lon}): "
                            f"MAT={mat_tec:.6f}, HDF5={hdf_tec:.6f}"
                        )
                
                # 比较 RMSE 值
                if not np.isclose(mat_rmse, hdf_rmse, rtol=1e-5, atol=1e-6):
                    mismatches += 1
                    if mismatches <= 5:  # 只记录前5个不匹配的例子
                        validation_result['issues'].append(
                            f"第 {doy} 天 {hour} 时 RMSE 不匹配 (lat={mat_lat}, lon={mat_lon}): "
                            f"MAT={mat_rmse:.6f}, HDF5={hdf_rmse:.6f}"
                        )
                
                total_comparisons += 1
        
        # 记录统计信息
        key = f"day_{doy}_hour_{hour}"
        validation_result['statistics'][key] = {
            'total_comparisons': total_comparisons,
            'mismatches': mismatches,
            'match_rate': (total_comparisons - mismatches) / total_comparisons if total_comparisons > 0 else 0
        }
    
    def validate_year(self, year, num_days=3):
        """验证指定年份的数据"""
        logging.info(f"开始验证 {year} 年的数据")
        
        try:
            # 加载数据
            mat_data = self.load_mat_data(year)
            hdf_data = self.load_hdf_data(year)
            
            # 选择随机天数进行验证
            selected_days = self.select_random_days(year, num_days)
            logging.info(f"选择的验证天数: {selected_days}")
            
            year_results = []
            for doy in selected_days:
                day_result = self.validate_day_data(year, doy, mat_data, hdf_data)
                year_results.append(day_result)
            
            return year_results
            
        except Exception as e:
            logging.error(f"验证 {year} 年数据时出错: {e}")
            return [{'year': year, 'doy': None, 'issues': [f"验证失败: {str(e)}"], 'statistics': {}}]
    
    def run_validation(self, years=None, days_per_year=3):
        """运行完整的验证流程"""
        if years is None:
            years = self.get_available_years()
        
        logging.info(f"开始验证数据处理管道，年份: {years}")
        
        all_results = []
        for year in years:
            year_results = self.validate_year(year, days_per_year)
            all_results.extend(year_results)
        
        self.validation_results = all_results
        return all_results
    
    def generate_report(self):
        """生成验证报告"""
        if not self.validation_results:
            return "没有验证结果可报告"
        
        report = []
        report.append("=" * 80)
        report.append("数据处理管道验证报告")
        report.append("=" * 80)
        
        total_validations = len(self.validation_results)
        successful_validations = sum(1 for r in self.validation_results if not r['issues'])
        
        report.append(f"\n总体统计:")
        report.append(f"  验证的年份-天数组合: {total_validations}")
        report.append(f"  成功验证: {successful_validations}")
        report.append(f"  发现问题: {total_validations - successful_validations}")
        report.append(f"  成功率: {successful_validations/total_validations*100:.1f}%")
        
        # 按年份分组报告
        years = sorted(set(r['year'] for r in self.validation_results))
        for year in years:
            year_results = [r for r in self.validation_results if r['year'] == year]
            report.append(f"\n{year} 年验证结果:")
            
            for result in year_results:
                if result['doy'] is None:
                    report.append(f"  整年验证失败")
                else:
                    report.append(f"  第 {result['doy']} 天:")
                
                if result['issues']:
                    report.append(f"    发现 {len(result['issues'])} 个问题:")
                    for issue in result['issues'][:10]:  # 最多显示10个问题
                        report.append(f"      - {issue}")
                    if len(result['issues']) > 10:
                        report.append(f"      ... 还有 {len(result['issues']) - 10} 个问题")
                else:
                    report.append(f"    ✓ 验证通过")
                
                # 显示统计信息
                if result['statistics']:
                    for key, stats in result['statistics'].items():
                        match_rate = stats['match_rate'] * 100
                        report.append(f"    {key}: {stats['total_comparisons']} 次比较, "
                                    f"{stats['mismatches']} 次不匹配, 匹配率: {match_rate:.1f}%")
        
        return "\n".join(report)

if __name__ == "__main__":
    # 设置随机种子以确保可重现的结果
    random.seed(42)
    
    validator = DataPipelineValidator()
    
    # 运行验证（可以指定特定年份，或留空验证所有年份）
    # results = validator.run_validation(years=[2020, 2021], days_per_year=2)
    results = validator.run_validation(days_per_year=2)  # 每年验证2天
    
    # 生成并打印报告
    report = validator.generate_report()
    print(report)
    
    # 保存报告到文件
    with open('validation_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    logging.info("验证完成，报告已保存到 validation_report.txt")
